<template>
  <page-wrapper :route-name="`mark-index::`">
    <div class="mark-index">
      <el-row style="padding-bottom: 8px">
        <el-col :span="24">
          <div class="flexBetween">
            <div class="flex" style="flex-grow: 1">
              <div class="flex">
                <strong style="width: 60px">配置选择</strong>
                <el-select v-model="modelValue.categoryIdSelect" @change="events.categoryChange" placeholder="请选择配置" filterable :disabled="isHistoryMode || isMissionMode" style="width: 180px">
                  <el-option v-for="item in modelValue.categoryList" :label="item.name" :value="item.id" />
                </el-select>
              </div>
              <div style="flex-grow: 1">
                <el-input
                  v-model="modelValue.search"
                  placeholder="请输入你要测评的query"
                  :prefix-icon="icons.Search"
                  @keydown.enter="events.doQuery"
                  :disabled="!showCategory || isHistoryMode || isMissionMode"
                >
                  <template #append>
                    <el-button @click="events.doQuery" :disabled="!showCategory || isHistoryMode">搜索</el-button>
                  </template>
                </el-input>
              </div>
              <div v-if="!showCategory" style="width: 260px"></div>
              <div v-if="showCategory" style="min-width: 260px">
                <el-popover placement="bottom" :width="200" trigger="hover">
                  <template #reference>
                    <strong class="cursor-pointer" style="margin-right: 20px">标注维度</strong>
                  </template>
                  <span>{{ categoryDimsStr }}</span>
                </el-popover>
                <el-popover placement="bottom" :width="200" trigger="hover">
                  <template #reference>
                    <strong class="cursor-pointer" style="margin-right: 20px">GoodUrl定义</strong>
                  </template>
                  <pre>{{ categoryUrlConfigStr.good }}</pre>
                </el-popover>
                <el-popover placement="bottom" :width="200" trigger="hover">
                  <template #reference>
                    <strong class="cursor-pointer">BadUrl定义</strong>
                  </template>
                  <pre>{{ categoryUrlConfigStr.bad }}</pre>
                </el-popover>
              </div>
              <div v-if="isMissionMode" style="min-width: 200px">
                <el-button :icon="icons.ArrowLeftBold" @click="events.preQuery" :disabled="missionValue.preDisabled" style="color: black"> 上一条 </el-button>
                <el-button @click="events.nextQuery" :disabled="missionValue.nextDisabled" style="color: black">
                  下一条<el-icon class="el-icon--right"><ArrowRightBold /></el-icon>
                </el-button>
              </div>
            </div>
            <el-dropdown trigger="click">
              <el-button type="primary" style="margin-left: 12px">更多操作</el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item :icon="icons.Open" @click="modelValue.displayScore = true" :disabled="modelValue.displayScore == true"> 显示策略数据 </el-dropdown-item>
                  <el-dropdown-item :icon="icons.TurnOff" @click="modelValue.displayScore = false" :disabled="modelValue.displayScore == false"> 隐藏策略数据 </el-dropdown-item>
                  <el-dropdown-item :icon="icons.ArrowDownBold" @click="events.unfoldContent" :disabled="dataC.isEmpty(modelValue.searchResult)"> 展开content </el-dropdown-item>
                  <el-dropdown-item :icon="icons.ArrowUpBold" @click="events.foldContent" :disabled="dataC.isEmpty(modelValue.searchResult)"> 折叠content </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <div class="flexBetween">
            <MarkStrategy
              v-if="showCategory"
              v-model="modelValue.strategyId1"
              @update:flowParamObj="events.updateStrategyList"
              :strategyList="categoryStrategyList1"
              :targetId="modelValue.targetId1"
              :categoryId="modelValue.categoryId"
              :markRecordId="modelValue.markRecordId"
              :regionList="modelValue.regionList"
              :sceneList="modelValue.sceneList"
              :ascribeList="modelValue.ascribeList"
              style="margin-right: 10px"
            ></MarkStrategy>
            <el-card v-if="showTip" class="tipCard">
              <div class="flexBetween">
                <strong>测评标注步骤提示：</strong>
                <el-button link :icon="icons.Close" @click="events.closeTip" style="font-size: 16px"> </el-button>
              </div>
              <el-row style="width: 100%">
                <el-col :span="24">第1步：在【结果统计】页面新建一个三级菜单，并对想要测评的策略进行配置；</el-col>
                <el-col :span="24">第2步：打开标注页面选择刚刚创建的三级菜单；</el-col>
                <el-col :span="24">第3步：输入query搜索进行逐条标注；标注完成后点击【自动归因】；</el-col>
                <el-col :span="24">第4步：在【结果统计】对应的子菜单里查看标注详情并进行策略结果分析、导出、修改等操作；</el-col>
              </el-row>
            </el-card>
            <MarkStrategy
              v-if="showCategory"
              v-model="modelValue.strategyId2"
              @update:flowParamObj="events.updateStrategyList"
              :strategyList="categoryStrategyList2"
              :targetId="modelValue.targetId2"
              :categoryId="modelValue.categoryId"
              :markRecordId="modelValue.markRecordId"
              :regionList="modelValue.regionList"
              :sceneList="modelValue.sceneList"
              :ascribeList="modelValue.ascribeList"
              style="margin-left: 10px"
            ></MarkStrategy>
          </div>
        </el-col>
      </el-row>

      <el-row style="position: relative">
        <el-tabs v-model="modelValue.activeName" @tab-change="events.tabChange" style="width: 100%">
          <el-tab-pane label="搜索结果" name="searchPane">
            <my-empty v-if="dataC.isEmpty(modelValue.searchResult)" :size="120" class="searchPane" />
          </el-tab-pane>
          <el-tab-pane label="Chat效果" name="chatPane">
            <my-empty v-if="dataC.isEmpty(modelValue.searchResult)" :size="120" class="chatPane" />
            <el-row v-if="!dataC.isEmpty(modelValue.searchResult)" :gutter="8" class="chatPane">
              <template v-for="(strategy, strategyIdx) in modelValue.searchResult">
                <el-col v-show="strategy.strategyId == modelValue.strategyId1 || strategy.strategyId == modelValue.strategyId2" :span="strategyIdx == 0 ? (multipleStrategy ? 12 : 24) : 12">
                  <div class="flex">
                    <strong style="margin-right: 5px; color: red">结果：</strong>
                    
                    <el-checkbox-group v-model="strategy.chat.value">
                      <el-checkbox v-for="item in chatMarkDimsInfo?.options" :label="item.name" :value="item.name" />
                    </el-checkbox-group>
                  </div>
                  <div class="chat-pane">
                    <MarkChat :chat="strategy.chat" :recallInfo="strategy.recallList" @reloadChat="events.reloadChat(strategy.strategyId)"></MarkChat>
                  </div>
                </el-col>
              </template>
            </el-row>
          </el-tab-pane>
        </el-tabs>
        <div v-if="isMissionMode || !dataC.isEmpty(modelValue.searchResult)" style="position: absolute; top: 4px; right: 0">
          <my-button type="primary" :icon="icons.MagicStick" @click="events.doAscribe" :disabled="!markComplete" disabledTips="未标注完成">自动归因</my-button>
          <my-button type="primary" :icon="icons.Document" @click="events.doSave">暂存</my-button>
        </div>
      </el-row>
    </div>
  </page-wrapper>
</template>

<script lang="ts" setup>
import { reactive, ref, watch, computed, onMounted, nextTick, render } from "vue";
import * as icons from "@element-plus/icons-vue";
import { keys, cloneDeep } from "lodash";
import { dataC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import useStore from "@/store";
import MarkStrategy from "./MarkStrategy.vue";
import MarkItem from "./MarkItem.vue";
import MarkTag from "./MarkTag.vue";
import MarkChat from "./MarkChat.vue";
import * as markApi from "@/api/eval-mark";
import * as missionApi from "@/api/eval-task";
import * as sceneApi from "@/api/scene";
import * as commonApi from "@/api/common";
import * as evaluationApi from "@/api/eval-evaluation";
import { MarkDimsInfo } from "@/types/mark";
import { el } from "element-plus/es/locale";
import { copyText, getText } from "@/utils/helpers";
const { $app, $router, proxy } = useCtx();
const { api } = useStore();

//从路由中获取参数
const markModeParam = $router.currentRoute.value.query.markMode;
const categoryIdParam = $router.currentRoute.value.query.categoryId;
const markRecordIdParam = $router.currentRoute.value.query.markRecordId;
const missionIdParam = $router.currentRoute.value.query.missionId;

//是否是任务模式
const isMissionMode = computed(() => {
  return markModeParam == "mission";
});

//是否是历史模式
const isHistoryMode = computed(() => {
  return markModeParam == "history";
});

const missionValue = reactive({
  missionId: missionIdParam,
  missionName: "",
  group: "",
  query: "",
  queryGroupId: "",
  preDisabled: false,
  nextDisabled: false,
});

const modelValue = reactive({
  categoryIdSelect: "", //选择的类别信息，因为可能要回滚
  markRecordId: "", //测评记录id
  categoryId: "", //选择的类别信息，用户已确认的
  categoryList: [], //类别列表
  search: "", //query字符串
  searchResult: [], //查询结果
  taskId: "", //查询结果对应的id
  activeName: "searchPane", //查询结果显示的面板
  markDimsDict: [], //标注的维度信息
  markSparkDict: [], //大模型标注的信息
  strategyId1: "", //左边选择的策略
  targetId1:"",
  strategyId2: "", //右边选择的策略
  targetId2:"",
  regionList: [], //区域列表
  sceneList: [], //场景策列列表
  ascribeList: [], //归因策略列表
  displayScore: false, //是否显示策略得分信息
  missionGroup: "", //mission下所属的组
  processList: [],
  chatValue: [],
  lastSend: Date.now(),
});

//是否显示提示
const showTip = ref(true);

//是否显示类别信息
const showCategory = computed(() => {
  console.log("showCategory computed", !dataC.isEmpty(modelValue.categoryId));
  return !dataC.isEmpty(modelValue.categoryId);
});

//类别的详细信息
const categoryInfo = computed(() => {
  console.log("categoryInfo computed", {
    showCategory: showCategory.value,
    categoryId: modelValue.categoryId,
    categoryList: modelValue.categoryList,
  });
  if (!showCategory.value) return undefined;
  return dataC.getItemByValue(modelValue.categoryList, modelValue.categoryId, "id");
});

//是否是多策略模式
const multipleStrategy = computed(() => {
  if (!showCategory.value) return false;
  return categoryInfo.value.strategyConfig.length > 1;
});

const categoryDimsStr = computed(() => {
  if (!showCategory.value) return [];
  const dims = (categoryInfo.value?.recallStandardConfig || []).map((item) => item.name);
  return dims.join("、");
});

interface DimOption {
  dimName: string;
  optionNames: string[];
}

interface DocClassifyRule {
  name: string;
  ruleGroup: Array<{
    dimOptions: DimOption[];
  }>;
}

const getString = (item: DocClassifyRule) => {
  if (!item || !item.ruleGroup) return "";

  return item.ruleGroup
    .map((group) => {
      return group.dimOptions
        .map((dim) => {
          return `${dim.dimName}: ${dim.optionNames.join("、")}`;
        })
        .join("; ");
    })
    .join("\n");
};

const categoryUrlConfigStr = computed(() => {
  if (!showCategory.value) return {};
  return { good: getString(categoryInfo.value.classifyRules.find((item) => item.name == "good")), bad: getString(categoryInfo.value.classifyRules.find((item) => item.name == "bad")) };
});

const categoryStrategyList1 = computed(() => {
  if (!showCategory.value) {
    return [];
  }
  return [categoryInfo.value.strategyConfig[0]];
});

const categoryStrategyList2 = computed(() => {
  if (!showCategory.value) return [];
  if (!multipleStrategy.value) return [];
  return categoryInfo.value.strategyConfig.slice(1, categoryInfo.value.strategyConfig.length);
});

//有效的标注维度
const markDimsInfo = computed<MarkDimsInfo>(() => {
  console.log("markDimsInfo computed");
  
  // 只获取dimensionList中的name和options字段
  if (!showCategory.value) return [];
  return (categoryInfo.value.recallStandardConfig || []).map((dim) => ({
    name: dim.name,
    code: dim.code,
    required: dim.required,
    optType: dim.optType,
    defaultValue: dim.defaultValue,
    options: dim.options.map((option) => {
      return {
        name: option.name,
        code: option.code,
        required: option.required,
        optType: option.optType,
        defaultValue: dim.defaultValue,
        feedbacks: option.feedbacks?.map((feedback) => {
          return {
            name: feedback.name,
            code: feedback.code,
            required: feedback.required,
            optType: feedback.optType,
            defaultValue: dim.defaultValue,
            content: feedback.content,
          };
        }),
      };
    }),
  }));
});

//chat推理结果标注字典
const chatMarkDimsInfo = computed(()=>{
  if (!showCategory.value || dataC.isEmpty(categoryInfo.value.chatStandardConfig)) return [];
  
  const dim = categoryInfo.value.chatStandardConfig[0];
  
  return {
    name: dim.name,
    code: dim.code,
    required: dim.required,
    optType: dim.optType,
    defaultValue: dim.defaultValue,
    options: dim.options.map((option) => {
      return {
        name: option.name,
        code: option.code,
        required: option.required,
        optType: option.optType,
        defaultValue: dim.defaultValue,
      };
    }),
  }
});

//是否标注完成
const markComplete = computed(() => {
  console.log("markComplete check");
  
  if (isMissionMode.value && dataC.isEmpty(modelValue.searchResult)) return true;
  if (dataC.isEmpty(modelValue.searchResult)) return false;

  return modelValue.searchResult.every((target, index1) => {
    return target.recallList.every((doc, index2) => {
      // 检查每个必填维度是否都已标注

      const res = (categoryInfo.value?.recallStandardConfig || []).every((dim) => {
        if (!dim.required) return true;
        
        if(!doc.markResult){
          return false;
        }

        // 在doc的markResult.dimList中查找当前维度
        if(!doc.markResult.dimsForm){
            updateEmit();
        }
        const markedDim = doc.markResult?.dimsForm?.find(item => item.name === dim.name);
        
        // 检查该维度是否有选中的选项
        return markedDim && !dataC.isEmpty(markedDim.value);
      });

      // console.log("target:",index1," doc:",index2, " 是否完成：", res);
      
      return res;
    });
  });
});

function updateEmit() {
  console.log("updateEmit start");
  
  modelValue.idx += 1;
  modelValue.searchResult.forEach((strategy) => {
    strategy.recallList.forEach((doc, docIdx) => {
      if (proxy.$refs[`markItemRef-${strategy.strategyId}-${docIdx}`] && proxy.$refs[`markItemRef-${strategy.strategyId}-${docIdx}`][0]) {
        proxy.$refs[`markItemRef-${strategy.strategyId}-${docIdx}`][0].emitDimsForm();
      }
    });
  });

  console.log("updateEmit end");
}


/**
 * 处理单个召回项的数据
 */
function processRecall(recall: any, index: number, strategy: any) {
  return {
    resultId: recall.markResult && recall.markResult.resultId ? recall.markResult.resultId : null,
    docIdx: index,
    url: recall.doc.url,
    title: recall.doc.title,
    markTargetId: strategy.targetId,
    ascribeList: recall.markResult.ascribeList,
    dimList: recall.markResult.dimList,
  };
}


function processChat(strategy: any) {
  // 确保 strategy.chat 存在
  if (!strategy.chat) {
    strategy.chat = {};
  }

  // 构建反馈对象
  const feedback = {
    content: strategy.chat.value || [],
  };

  // 更新或创建 markResult

  strategy.chat = {
    ...strategy.chat,
    markResult: {
      ...(strategy.chat.markResult || {}),
      resultId: strategy.chat.markResult?.resultId || null,
      feedbackList: [feedback]
    }
  };

  return strategy.chat.markResult;
}

/**
 * 处理单个策略的数据
 */
function processStrategy(strategy: any) {
  return {
    targetId: strategy.targetId,
    recallList: strategy.recallList.map((recall: any, index: number) => processRecall(recall, index, strategy)),
    chat: processChat(strategy),
  };
}

/**
 * 获取保存请求的数据
 */
const getSaveRequest = () => {
  updateEmit();
  const request = {
    id: modelValue.markRecordId,
    targets: modelValue.searchResult.map(processStrategy),
  };

  console.log("request", request);
  return request;
};

const events = reactive({
  updateStrategyList: (strategyList: any, strategyId: string) => {
    modelValue.categoryList = modelValue.categoryList.map((item) => {
      item.strategyConfig = item.strategyConfig.map((i) => {
        if (i.id == strategyId) {
          Object.assign(i, strategyList);
        }
        return i;
      });
      return item;
    });
  },
  //mission模式获取上一条query
  preQuery: async () => {
    //获取上一条的markRecordId
    missionApi.myMissionQuery(missionValue.missionId, modelValue.markRecordId, "prev").then((result) => {
      if (!result.data.complete) {
        $app.$message.warning("当前query未标注完成!");
      } else if (result.data.recordId) {
        events.findByMarkRecordId(result.data.recordId);
        missionValue.preDisabled = false;
        missionValue.nextDisabled = false;
      } else {
        $app.$message.warning("已经是第一条了!");
        missionValue.preDisabled = true;
      }
    });
  },
  //mission模式获取下一条query
  nextQuery: async () => {
    //获取下一条的markRecordId 或者 query
    missionApi.myMissionQuery(missionValue.missionId, modelValue.markRecordId, "next").then((result) => {
      if (!result.data.complete) {
        $app.$message.warning("当前query未标注完成!");
      } else if (result.data.recordId) {
        events.findByMarkRecordId(result.data.recordId);
        missionValue.preDisabled = false;
        missionValue.nextDisabled = false;
      } else if (result.data.query) {
        modelValue.search = result.data.query;
        events.doQuery();
        missionValue.preDisabled = false;
        missionValue.nextDisabled = false;
      } else {
        $app.$message.warning("已经是最后一条了!");
        missionValue.nextDisabled = true;
      }
    });
  },
  //关闭步骤提示
  closeTip: () => {
    showTip.value = false;
  },
  //展开content
  unfoldContent: () => {
    modelValue.searchResult.forEach((strategy, strategyIdx) => {
      strategy.recallList.forEach((doc, docIdx) => {
        proxy.$refs[`markItemRef-${strategy.strategyId}-${docIdx}`][0].unfoldContent();
      });
    });
  },
  //折叠content
  foldContent: () => {
    modelValue.searchResult.forEach((strategy, strategyIdx) => {
      strategy.recallList.forEach((doc, docIdx) => {
        proxy.$refs[`markItemRef-${strategy.strategyId}-${docIdx}`][0].foldContent();
      });
    });
  },
  //切换为体验模式
  changeMode: () => {
    $router.push({
      name: `experience-index`,
    });
  },
  //清空查询结果
  clearQuery: () => {
    modelValue.markRecordId = "";
    modelValue.searchResult = [];
    // modelValue.activeName = "searchPane";
  },
  getInputArgs: (sceneProcessId: string, regionCode: string) => {
    return new Promise((resolve, reject) => {
      commonApi
        .getProcessInputArgs(sceneProcessId, regionCode)
        .then((result) => {
          resolve(
            result.inputArgs.filter((item) => {
              return item.key != "query";
            })
          );
        })
        .catch((error) => {
          reject(error);
        });
    });
  },
  //类别变化时
  categoryChange: async (value) => {
    const fun = async () => {
      //先清空当前页面标注信息
      events.clearQuery();
      //再切换为新的category信息
      const category = modelValue.categoryList.find((item) => item.id == value);
      category.strategyConfig = await Promise.all(
        category.strategyConfig.map(async (item) => {
          return {
            ...item,
            processId: item.processId,
            regionCode: item.regionCode,
            strategyId: item.id,
            inputArgs: item.processId ? item.inputArgs || (await events.getInputArgs(item.processId, item.regionCode)) : [],
          };
        })
      );
      modelValue.categoryId = modelValue.categoryIdSelect;
      modelValue.category = category;
      modelValue.strategyId1 = categoryInfo.value.strategyConfig[0].id;
      if (multipleStrategy.value) {
        modelValue.strategyId2 = categoryInfo.value.strategyConfig[1].id;
      }
    };
    if (!isMissionMode.value && !dataC.isEmpty(modelValue.categoryId) && !dataC.isEmpty(modelValue.searchResult)) {
      $app
        .$confirm({ title: `此操作开始新任务,是否继续?` })
        .then(() => {
          fun();
        })
        .catch(() => {
          modelValue.categoryIdSelect = modelValue.categoryId;
        });
    } else {
      fun();
    }
  },
  //搜索和chat面板切换时
  tabChange: (tabPaneName: string) => {
    if (tabPaneName == "chatPane") {
      modelValue.searchResult.forEach((strategy) => {
        if (dataC.isEmpty(strategy.chat.content)) {
          events.reloadChat(strategy.strategyId);
        }
      });
    }
  },
  //获取chat验证结果
  reloadChat: async (strategyId: String) => {
    try {
      const result = await markApi.getMarkReocordById(modelValue.markRecordId, false, false, true);

      if (!dataC.isEmpty(strategyId)) {
        const response = result.data.targets.find((item: SearchResult) => item.strategyId === strategyId);
        const searchResult = modelValue.searchResult.find((item: SearchResult) => item.strategyId === strategyId);
        if (response && searchResult) {
          searchResult.chat = response.chat;
        }
      }
    } catch (error) {
      console.error("Failed to reload chat:", error);
      $app.$message.error("重新加载聊天失败");
    }
  },
  //单query搜索
  doQuery: async () => {

    //防止页面频繁点击
    if(isQuickClick()){
      //频繁操作 
      return;
    }

    const fun = async () => {
      //先清空当前页面标注信息
      events.clearQuery();
      if (dataC.isEmpty(modelValue.search)) {
        $app.$message.warning("测评的query不可为空!");
        return;
      }
      const createResult = await markApi.createMarkRecord({
        categoryId: modelValue.categoryId,
        query: modelValue.search,
        missionId: missionValue.missionId,
        processList: modelValue.category.strategyConfig.map((strategy) => {
          const params = {};
          strategy.inputArgs.forEach((i) => {
            params[i.key] = i.value;
          });
          return {
            processId: strategy.processId,
            regionCode: strategy.regionCode,
            strategyId: strategy.id,
            payload: params,
          };
        }),
      });
      modelValue.markRecordId = createResult.data;

      //再请求新的页面标注信息
      await events.findByMarkRecordId(createResult.data);
    };
    if (!isMissionMode.value && !dataC.isEmpty(modelValue.searchResult)) {
      $app.$confirm({ title: `此操作开始新任务,是否继续?` }).then(() => {
        fun();
      });
    } else {
      fun();
    }
  },

  findByMarkRecordId: async (markRecordId: String) => {
    await markApi.getMarkReocordById(markRecordId, true, false, true).then((result) => {
      events.render(result);
    });
  },

  //自动归因
  doAscribe: async () => {
    const saveResult = await markApi.doSave(getSaveRequest(), missionValue.missionId, missionValue.group);
    // events.findByMarkRecordId(modelValue.markRecordId);
    const result = await markApi.doAscribe(modelValue.markRecordId);
    $app.$message.success("自动归因成功");
    // const saveResult2 = await markApi.doSave(getSaveRequest(), missionValue.missionId, missionValue.group);
    events.findByMarkRecordId(modelValue.markRecordId);
  },
  //保存
  doSave: () => {
    markApi.doSave(getSaveRequest(), missionValue.missionId, missionValue.group).then((result) => {
      events.findByMarkRecordId(modelValue.markRecordId);
      $app.$message.success("暂存成功");
    });
  },

  // 将获取到的测评记录渲染到页面
  render: (result: any) => {
    events.clearQuery();
    modelValue.markRecordId = result.data.id;
    modelValue.search = result.data.query;
    modelValue.categoryId = result.data.categoryId;
    modelValue.categoryIdSelect = modelValue.categoryId;
    modelValue.strategyId1 = categoryInfo.value.strategyConfig[0].id;
    modelValue.targetId1 = dataC.getItemByValue(result.data.targets, modelValue.strategyId1, "strategyId").targetId;
    if (multipleStrategy.value) {
      modelValue.strategyId2 = categoryInfo.value.strategyConfig[1].id;
      modelValue.targetId2 = dataC.getItemByValue(result.data.targets, modelValue.strategyId2, "strategyId").targetId;
    }
    //判断搜索结果是否为空
    const emptyFlag = result.data.targets.every((item) => dataC.isEmpty(item.recallList));
    if (emptyFlag) {
      $app.$message.warning("搜索结果为空!");
      return;
    }

    //赋值searchResult,后端返回得结果乱序，需要按照策略进行排序
    categoryInfo.value.strategyConfig.forEach((strategyItem) => {
      const strategy = dataC.getItemByValue(result.data.targets, strategyItem.id, "strategyId");
      console.log("strategy:", strategy);
      //保证chat.markResult中有值
      if(strategy && !dataC.isEmpty(strategy.chat)){
        strategy.chat = {...strategy.chat, value: strategy.chat.markResult?.feedbackList?.[0].content || []}
      }

      if(dataC.isEmpty(strategy.chat)){
        strategy.chat = {
          value:[]
        }
      }

      modelValue.searchResult.push(strategy);
    });

    events.addAscribeListCheck();
  },

  addAscribeListCheck: () => {
    // 为 strategy[x].recallList?.markResult?.ascribeList 中所有元素增加 check 字段，check = dataC.isEmpty(ascribeItem.remark)
    modelValue.searchResult.forEach((strategy) => {
      strategy.recallList?.forEach((doc) => {
        if (doc.markResult && Array.isArray(doc.markResult.ascribeList)) {
          doc.markResult.ascribeList.forEach((ascribeItem) => {
            ascribeItem.check = !dataC.isEmpty(ascribeItem.remark);
          });
        }
      });
    });
  },
});

/**
 * 判断是否为快速点击  true：为快速点击 应禁止    false: 否， 可继续下一步操作
 */
function isQuickClick(){
  const now = Date.now();
  if (now - modelValue.lastSend > 2000) { // 2000ms 内只触发一次
    modelValue.lastSend = now;
    return false;
  }else{
    return true;
  }
}

onMounted(async () => {
  //获取类别列表,后端返回的为三层树结构,这里我们只要第三层的数据
  const res1 = await markApi.getMarkCategoryList();
  //遍历一级目录
  res1.data.forEach((item1) => {
    //遍历二级目录
    item1.children?.forEach((item2) => {
      //获取所有三级目录 加入到modelValue.categoryList中
      modelValue.categoryList.push(...(item2.children || []));
    });
  });

  // todo  调试使用  待删除  设置默认的categoryId和categoryIdSelect
  // if (modelValue.categoryList.length > 0) {
  //   modelValue.categoryIdSelect = "1910532339696558333";
  //   modelValue.categoryId = "1910532339696558333";
  //   modelValue.search = "糖尿病原因";

  //   events.categoryChange("1910532339696558333");
  // }

  //获取区域列表
  modelValue.regionList = await api.getMetaRegionList();


  modelValue.sceneList.push(...(await sceneApi.getSceneVersionListByName()).data);
  modelValue.ascribeList.push(...(await evaluationApi.getStrategyVersion(0)).data);
  modelValue.ascribeList.push(...(await evaluationApi.getStrategyVersion(1)).data);

  if (isHistoryMode.value) {
    // 历史模式  todo 调用find,参数通过路由获取
    events.findByMarkRecordId(markRecordIdParam);
    // events.doReplay(categoryIdParam, taskIdParam);
  } else if (isMissionMode.value) {
    // Mission模式
    const mission = await missionApi.myMissionDetail(missionIdParam);
    missionValue.missionName = mission.data.missionName;
    missionValue.group = mission.data.group;
    //添加到页面路由上
    $router.push({
      name: `mark-index`,
      query: {
        markMode: "mission",
        missionId: missionIdParam,
        group: missionValue.group,
      },
    });

    // 设置配置信息
    modelValue.categoryIdSelect = mission.data.categoryId;
    events.categoryChange(modelValue.categoryIdSelect);

    // 如果没有历史信息获取即将要标注的query
    missionValue.query = mission.data.query;

    // 检查是否有历史标注任务
    await handleReplayProbe();
  } else {
    // 新标注任务模式
    await handleReplayProbe();
  }

  // 处理历史标注任务探测逻辑
  async function handleReplayProbe() {
    const result = await markApi.findUnfinished(missionValue.missionId, missionValue.group);

    if (!dataC.isEmpty(result.data)) {
      try {
        await $app.$confirm({ title: `恢复任务?` });
        events.render(result);
      } catch {
        // todo 不需要操作  后端会再下一次搜索时删除历史未完成的测评记录
      }
    } else if (isMissionMode.value) {
      // 没有历史任务，直接查询
      if (missionValue.query) {
        modelValue.search = missionValue.query;
        events.doQuery();
      } else {
        events.preQuery();
      }
    }
  }
  updateEmit();
});
</script>

<style lang="scss" scoped>
.mark-index {
  padding: 8px 15px;
  line-height: 23px;
  ::v-deep .el-input-group__append {
    background-color: var(--el-color-primary);
    color: #fff;
  }
  .flexBetween {
    > .flex {
      div + div {
        margin-left: 12px;
      }
    }
  }
  ::v-deep {
    .icon-copy {
      color: $primary-color;
      cursor: pointer;
    }
  }
  ::v-deep .el-tabs__header {
    padding: 0;
  }
  ::v-deep .el-tabs__content {
    padding: 8px 0 0 0;
  }

  .tipCard {
    height: 125px;
    overflow-y: auto;
    font-size: 14px;
    background-color: #e9f5ff;
    ::v-deep .el-card__body {
      padding: 3px 12px;
    }
  }

  .searchPane {
    height: calc(100vh - 290px);
    ::v-deep .el-col {
      height: 100%;
      .mark-pane {
        height: 100%;
        padding: 0 6px;
        overflow-y: auto;
      }
    }
  }
  .chatPane {
    height: calc(100vh - 290px);
    ::v-deep .el-col {
      height: 100%;
      .chat-pane {
        height: calc(100% - 40px);
        overflow-y: auto;
        padding: 2px 3px;
      }
    }
  }

  .cursor-pointer {
    cursor: pointer;
  }
}

::v-deep .el-dropdown-menu__item {
  flex-direction: row !important;
  align-items: center !important;
}
</style>
<style lang="scss">
.position-dialog {
  .el-dialog__header {
    padding: 0;
  }
  .el-dialog__body {
    height: calc(100vh - 72px);
    padding: 0;
  }
  .container {
    height: 100%;
    padding: 0;
  }
}
</style>