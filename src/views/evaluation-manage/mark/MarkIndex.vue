<template>
  <page-wrapper :route-name="`mark-index::`">
    <div class="mark-index">
      <el-row class="first-row">
        <el-col :span="24">
          <div class="flexBetween">
            <div class="flex" style="flex-grow: 1">
              <div class="flex">
                <strong style="width: 60px">我的任务</strong>
                <el-select v-model="missionId" @change="events.missionChange" placeholder="请选择测评任务" filterable
                  style="width: 180px">
                  <el-option v-for="item in myMissionList" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
              </div>

              <div>
                <span style="white-space: nowrap">完成进度：{{ complatePercentage }}</span>
              </div>

              <div style="flex-grow: 1">
                <el-input v-model="modelValue.query" disabled="true" placeholder="请输入你要测评的query"
                  :prefix-icon="icons.Search" @keydown.enter="events.doQuery">
                  <template #append>
                    <el-button @click="events.doQuery">搜索</el-button>
                  </template>
                </el-input>
              </div>
              <div style="min-width: 200px">
                <el-button :icon="icons.ArrowLeftBold" @click="events.preQuery" :disabled="modelValue.preDisabled"
                  style="color: black">
                  上一条
                </el-button>
                <el-button :icon="icons.ArrowRightBold" @click="events.nextQuery" :disabled="modelValue.nextDisabled"
                  style="color: black">
                  下一条
                </el-button>
              </div>
            </div>
            <el-dropdown trigger="click">
              <el-button type="primary" style="margin-left: 12px">更多操作</el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item :icon="icons.Open" @click="modelValue.displayScore = true"
                    :disabled="modelValue.displayScore == true">
                    显示策略数据
                  </el-dropdown-item>
                  <el-dropdown-item :icon="icons.TurnOff" @click="modelValue.displayScore = false"
                    :disabled="modelValue.displayScore == false">
                    隐藏策略数据
                  </el-dropdown-item>
                  <el-dropdown-item :icon="icons.ArrowDownBold" @click="events.unfoldContent"
                    :disabled="dataC.isEmpty(modelValue.searchResult)">
                    展开content
                  </el-dropdown-item>
                  <el-dropdown-item :icon="icons.ArrowUpBold" @click="events.foldContent"
                    :disabled="dataC.isEmpty(modelValue.searchResult)">
                    折叠content
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </el-col>
      </el-row>

      <el-row class="second-row">
        <el-switch v-model="modelValue.isQueryIgnore" inactive-text="是否弃标"></el-switch>
        <div v-if="modelValue.isQueryIgnore">
          <AnnotationTreeComponent :model-value="modelValue.queryIgnore.ignore"
            @update:modelValue="handleQueryIgnoreChange" />
          <el-input v-model="modelValue.queryIgnore.remark" type="textarea" placeholder="请输入备注"></el-input>
        </div>
        <el-button type="primary" :icon="icons.Document" @click="saveMarkResult(false)">保存</el-button>
      </el-row>

      <div class="doc-annotation-list">
        <DocAnnotation v-for="(item, index) in modelValue.docList" ref="docAnnotationRef" :key="index"
          :mark-record-id="modelValue.markRecordId" :strategy-id="item.strategyId" :target-id="item.targetId"
          :doc-data="item.doc" :doc-index="index" :score-ranks="item.scoringRanks"
          :display-score="modelValue.displayScore" :standard-config="modelValue.currentMission.standardConfig"
          :extend-fields="modelValue.currentMission.extendFields" :annotation-data="bindMarkValue2DimsForm(item)"
          @update:annotationData="(newValue: any) => handleDocAnnotationChange(index, newValue)"
          :is-ignore="item.ignore" @update:isIgnore="(newValue: any) => handleDocIgnoreStatusChange(index, newValue)"
          :remark="item.remark" @update:remark="(newValue: any) => handleRemarkChange(index, newValue)"
          style="margin-bottom: 16px" />
      </div>
    </div>
  </page-wrapper>
</template>

<script lang="ts" setup>
import { reactive, ref, computed, onMounted, watch } from "vue";
import { dataC } from "turing-plugin";
import * as icons from "@element-plus/icons-vue";
import * as missionApi from "@/api/eval-task";
import * as markApi from "@/api/eval-mark";
import DocAnnotation from "./DocAnnotation.vue";
import AnnotationTreeComponent from "./AnnotationTreeComponent.vue";
import useCtx from "@/hooks/useCtx";

const { $app, proxy, $router } = useCtx();

const missionId = ref("");

const myMissionList = ref<any[]>([]);
const modelValue = reactive({
  currentMissionId: "",
  currentMission: {},
  isQueryIgnore: false,
  queryIgnore: { ignore: [], remark: "" },
  markRecordId: "",
  query: "",
  searchResult: [],
  displayScore: false,
  preDisabled: false,
  nextDisabled: false,
  docList: [],
});

const complatePercentage = computed(() => {
  modelValue.currentMission = myMissionList.value.find(
    (item) => item.id == modelValue.currentMissionId
  );
  return modelValue.currentMission
    ? `${(
      (modelValue.currentMission.completeQueryCount /
        modelValue.currentMission.assignQueryCount) *
      100
    ).toFixed(0)}%`
    : "-";
});

const bindMarkValue2DimsForm = (docItem: any) => docItem.dimsList || [];

const saveMarkResult = async (completeCheck: boolean) => {
  modelValue.docList.forEach((item) => {
    const recall =
      modelValue.searchResult[item.targetIdx].recallList[item.docIdx];
    recall.markResult = recall.markResult || { dimsList: [] };
    recall.markResult.dimsList = item.dimsList;
    recall.markResult.ignore = item.ignore;
    recall.markResult.remark = item.remark;
  });

  const result = await markApi.doSave(
    getSaveRequest(),
    modelValue.currentMissionId,
    completeCheck
  );
  if (!completeCheck) {
    $app.$message.success("保存成功");
    return true;
  }
  return (
    result.data.complete || ($app.$message.warning(result.data.msg), false)
  );
};

const getSaveRequest = () => ({
  id: modelValue.markRecordId,
  targets: modelValue.searchResult.map((strategy) => ({
    targetId: strategy.targetId,
    recallList: strategy.recallList.map((recall, index) => ({
      resultId: recall.markResult?.resultId || null,
      docIdx: index,
      url: recall.doc.url,
      title: recall.doc.title,
      markTargetId: strategy.targetId,
      ascribeList: recall.markResult?.ascribeList,
      dimsList: recall.markResult?.dimsList,
      ignore: recall.markResult?.ignore,
      remark: recall.markResult?.remark,
    })),
  })),
  queryIgnore: modelValue.isQueryIgnore ? modelValue.queryIgnore : null,
});

const render = async (result: any) => {
  modelValue.docList = [];
  modelValue.searchResult = result.data.targets;
  modelValue.query = result.data.query;
  modelValue.currentMissionId = result.data.missionId;
  missionId.value = result.data.missionId;
  modelValue.isQueryIgnore = result.data.queryIgnore ? true : false;
  modelValue.queryIgnore = result.data.queryIgnore || {
    ignore: myMissionList.value.find(
      (item) => item.id == modelValue.currentMissionId
    ).standardConfig.docIgnore,
    remark: "",
  };
  modelValue.markRecordId = result.data.id;

  result.data.targets.forEach((target, index) => {
    target.recallList?.forEach((doc, docIdx) => {
      modelValue.docList.push({
        targetIdx: index,
        docIdx: docIdx,
        doc: doc.doc,
        scoringRanks: doc.scoringRanks,
        dimsList: doc.markResult?.dimsList || [],
        ignore: doc.markResult?.ignore || false,
        remark: doc.markResult?.remark || "",
        targetId: target.targetId,
        strategyId: target.strategyId,
      });
    });
  });
};

const initModelValue = () => {
  modelValue.docList = [];
  modelValue.markRecordId = "";
  modelValue.searchResult = [];
  modelValue.query = "";
  modelValue.isQueryIgnore = false;
  modelValue.queryIgnore = { ignore: [], remark: "" };
};

const events = {
  missionChange: async () => {
    if (!dataC.isEmpty(modelValue.markRecordId)) {
      try {
        await $app.$confirm({ title: `此操作将切换任务,是否继续?` });
        events.switchMission();
      } catch {
        missionId.value = dataC.isEmpty(modelValue.currentMissionId)
          ? ""
          : modelValue.currentMissionId;
      }
    } else {
      events.switchMission();
    }
  },
  switchMission: async () => {
    modelValue.currentMissionId = missionId.value;
    //清空历史数据
    initModelValue();
    await findUnfinished();

    //如果没有未标注完成的记录  则自动获取下一条记录
    if (dataC.isEmpty(modelValue.markRecordId)) {
      await events.switchQuery();
    }
  },
  doQuery: async () => {
    modelValue.docList = [];
    modelValue.markRecordId = "";
    modelValue.searchResult = [];

    const result = await markApi.createMarkRecord({
      query: modelValue.query,
      missionId: modelValue.currentMissionId,
    });
    modelValue.markRecordId = result.data;
    await events.findByMarkRecordId(modelValue.markRecordId);
  },
  findByMarkRecordId: async (markRecordId: string) => {
    const result = await markApi.getMarkReocordById(
      markRecordId,
      true,
      false,
      true
    );
    await render(result);
  },
  preQuery: async () => {
    if (!(await saveMarkResult(true))) return;
    const result = await missionApi.myMissionQuery(
      modelValue.currentMissionId,
      modelValue.markRecordId,
      "prev"
    );
    if (result.data.recordId) {
      await events.findByMarkRecordId(result.data.recordId);
      modelValue.preDisabled = false;
      modelValue.nextDisabled = false;
    } else {
      $app.$message.warning("已经是第一条了!");
      modelValue.preDisabled = true;
    }
  },
  nextQuery: async () => {
    if (!(await saveMarkResult(true))) return;

    //更新任务列表
    myMissionList.value = (await missionApi.myMission()).data;

    events.switchQuery();
    modelValue.preDisabled = false;
    modelValue.nextDisabled = false;
  },
  switchQuery: async () => {
    const result = await missionApi.myMissionQuery(
      modelValue.currentMissionId,
      modelValue.markRecordId,
      "next"
    );
    if (result.data.recordId) {
      await events.findByMarkRecordId(result.data.recordId);
    } else if (result.data.query) {
      modelValue.query = result.data.query;
      await events.doQuery();
    } else {
      $app.$message.warning("已经是最后一条了!");
    }
  },
  unfoldContent: () =>
    modelValue.docList.forEach((item, index) =>
      proxy.$refs.docAnnotationRef[index].unfoldContent()
    ),
  foldContent: () =>
    modelValue.docList.forEach((item, index) =>
      proxy.$refs.docAnnotationRef[index].foldContent()
    ),
};

const handleQueryIgnoreChange = (newValue: any[]) =>
  (modelValue.queryIgnore.ignore = newValue);
const handleDocAnnotationChange = (index: number, newValue: any) =>
  (modelValue.docList[index].dimsList = newValue);
const handleDocIgnoreStatusChange = (index: number, newValue: boolean) =>
  (modelValue.docList[index].ignore = newValue);
const handleRemarkChange = (index: number, newValue: string) =>
  (modelValue.docList[index].remark = newValue);

const findUnfinished = async () => {
  const result = await markApi.findUnfinished(modelValue.currentMissionId);
  if (dataC.isEmpty(result.data)) return;

  try {
    await $app.$confirm({ title: `恢复任务?` });
    await render(result);
  } catch {
    // 不需要操作，后端会处理
  }
};

onMounted(async () => {
  //1.获取所有任务
  myMissionList.value = (await missionApi.myMission()).data;

  //2.获取所有的路由中的任务id 和 测评记录id（存在表示为历史标注回显）
  let chicenMissionId = $router.currentRoute.value.query.missionId;
  let markRecordId = $router.currentRoute.value.query.markRecordId;

  if (markRecordId) {
    console.log("历史标注回显");
    await events.findByMarkRecordId(markRecordId);
  } else {

    if (myMissionList.value.length && dataC.isEmpty(chicenMissionId)) {
      chicenMissionId = myMissionList.value[0].id;
      await findUnfinished();
    }

    if (!myMissionList.value.length) {
      console.log("没有测评任务");
      return;
    }

    modelValue.currentMissionId = chicenMissionId;
    missionId.value = chicenMissionId

    //如果没有未标注完成的记录  则自动获取下一条记录
    if (dataC.isEmpty(modelValue.markRecordId)) {
      await events.switchQuery();
    }
  }

});
</script>

<style lang="scss" scoped>
.mark-index {
  display: flex;
  flex-direction: column;
  padding: 8px 15px;
  line-height: 23px;
  overflow-y: hidden;

  .first-row {
    flex: 0 0 40px;
    padding-bottom: 8px;
  }

  .second-row {
    flex: 0 0 auto;
    margin-bottom: 10px;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }

  .doc-annotation-list {
    flex: 1;
    min-height: 0;
    overflow-y: auto;
  }

  ::v-deep .el-input-group__append {
    background-color: var(--el-color-primary);
    color: #fff;
  }

  .flexBetween {
    display: flex;
    justify-content: space-between;

    >.flex {
      div+div {
        margin-left: 12px;
      }
    }
  }

  .searchPane {
    height: calc(100vh - 290px);

    ::v-deep .el-col {
      height: 100%;

      .mark-pane {
        height: 100%;
        padding: 0 6px;
        overflow-y: auto;
      }
    }
  }

  .chatPane {
    height: calc(100vh - 290px);

    ::v-deep .el-col {
      height: 100%;

      .chat-pane {
        height: calc(100% - 40px);
        overflow-y: auto;
        padding: 2px 3px;
      }
    }
  }
}

::v-deep .el-dropdown-menu__item {
  flex-direction: row !important;
  align-items: center !important;
}
</style>
